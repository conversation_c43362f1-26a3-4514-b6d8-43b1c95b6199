name: Rust CI/CD

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'

    steps:
      - name: ✅ Checkout
        uses: actions/checkout@v3

      - name: 🔧 安装系统依赖
        run: |
          sudo apt-get update
          sudo apt-get install -y musl-tools pkg-config sshpass

      - name: 🦀 安装 Rust + musl
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          source $HOME/.cargo/env
          rustup default stable
          rustup target add x86_64-unknown-linux-musl

      - name: 🔨 构建项目（静态链接）
        run: cargo build --release --target x86_64-unknown-linux-musl

      - name: 🚀 部署到服务器
        run: |
          mkdir -p deploy
          cp target/x86_64-unknown-linux-musl/release/aishoucang-mns deploy/

          # 生成 systemd 服务文件（保留）
          cat > deploy/aishoucang.service << EOF
          [Unit]
          Description=AI收藏 Rust服务
          After=network.target

          [Service]
          Type=simple
          User=root
          WorkingDirectory=/root/aishoucang-mns
          ExecStart=/root/aishoucang-mns/aishoucang-mns
          Restart=on-failure
          RestartSec=5

          [Install]
          WantedBy=multi-user.target
          EOF

          # 设置 SSH 密码
          export SSHPASS='Qtt$123456'

          # 上传程序与 systemd 服务文件
          echo "🔧 准备服务器目录..."
          sshpass -e ssh -o StrictHostKeyChecking=no root@************ << 'ENDSSH_PREP'
            # 确保目录存在并有正确的权限
            mkdir -p /root/aishoucang-mns
            chmod 755 /root/aishoucang-mns
            # 检查目录是否创建成功
            if [ -d "/root/aishoucang-mns" ]; then
              echo "✅ 目录创建成功: /root/aishoucang-mns"
              ls -la /root/aishoucang-mns
            else
              echo "❌ 目录创建失败: /root/aishoucang-mns"
              exit 1
            fi
          ENDSSH_PREP

          # 检查本地文件是否存在
          echo "🔍 检查本地文件..."
          if [ ! -f "deploy/aishoucang-mns" ]; then
            echo "❌ 错误：本地可执行文件不存在"
            ls -la deploy/
            exit 1
          fi

          if [ ! -f "deploy/aishoucang.service" ]; then
            echo "❌ 错误：本地服务文件不存在"
            ls -la deploy/
            exit 1
          fi

          echo "✅ 本地文件检查通过"

          echo "📤 上传可执行文件..."
          # 先停止服务，释放文件
          echo "🛑 先停止服务，释放文件..."
          sshpass -e ssh -o StrictHostKeyChecking=no root@************ << 'ENDSSH_STOP'
            # 停止服务
            systemctl stop aishoucang.service
            sleep 2

            # 检查是否有残留进程并强制终止
            if pgrep -f "aishoucang-mns"; then
              echo "发现残留进程，正在强制终止..."
              pkill -9 -f "aishoucang-mns"
              sleep 1
            fi

            echo "✅ 服务已停止"
          ENDSSH_STOP

          # 上传到临时文件
          echo "📤 上传到临时文件..."
          sshpass -e scp -v -o StrictHostKeyChecking=no deploy/aishoucang-mns root@************:/root/aishoucang-mns/aishoucang-mns.new

          # 检查上传是否成功
          if [ $? -ne 0 ]; then
            echo "❌ 可执行文件上传失败"
            exit 1
          fi

          # 替换文件
          echo "🔄 替换文件..."
          sshpass -e ssh -o StrictHostKeyChecking=no root@************ << 'ENDSSH_REPLACE'
            mv /root/aishoucang-mns/aishoucang-mns.new /root/aishoucang-mns/aishoucang-mns
            chmod +x /root/aishoucang-mns/aishoucang-mns
            echo "✅ 文件替换成功"
          ENDSSH_REPLACE

          echo "📤 上传服务文件..."
          sshpass -e scp -v -o StrictHostKeyChecking=no deploy/aishoucang.service root@************:/etc/systemd/system/

          # 检查上传是否成功
          if [ $? -ne 0 ]; then
            echo "❌ 服务文件上传失败"
            exit 1
          fi

          # 启动服务
          sshpass -e ssh -o StrictHostKeyChecking=no root@************ << 'ENDSSH'
            echo "🔧 检查可执行文件..."
            if [ -f "/root/aishoucang-mns/aishoucang-mns" ]; then
              echo "✅ 可执行文件存在，准备启动服务"
            else
              echo "❌ 错误：可执行文件不存在"
              ls -la /root/aishoucang-mns/
              exit 1
            fi

            echo "🔄 正在启动服务..."

            # 重新加载服务配置并启动
            systemctl daemon-reload
            systemctl enable aishoucang.service
            systemctl start aishoucang.service

            echo "✅ 服务已重启"
            echo "📋 Rust 服务状态:"
            systemctl status aishoucang.service --no-pager | head -n 20

            # 等待服务完全启动
            echo "⏳ 等待服务完全启动..."
            sleep 8

            # 检查服务是否正常运行（使用端口8080）
            if curl -s http://localhost:8080/test/ping | grep -q "pong"; then
              echo "✅ 服务已成功启动并响应 ping 请求"
              echo "🔍 检查服务日志（最近10条）:"
              journalctl -u aishoucang.service -n 10 --no-pager
            else
              echo "⚠️ 警告：服务可能未正常启动"
              echo "🔍 检查错误日志:"
              journalctl -u aishoucang.service -n 50 --no-pager

              # 检查端口是否被占用
              echo "🔍 检查端口8080状态:"
              netstat -tulpn | grep 8080 || echo "端口8080未被占用"

              # 尝试再次启动
              echo "🔄 尝试再次启动服务..."
              systemctl restart aishoucang.service
              sleep 5

              # 再次检查
              if curl -s http://localhost:8080/test/ping | grep -q "pong"; then
                echo "✅ 第二次尝试成功：服务已启动"
              else
                echo "❌ 服务启动失败，请手动检查问题"
                exit 1
              fi
            fi

            # 服务已成功启动，显示完成信息
            echo "✅ 部署和服务启动完成"
            echo "� 服务状态摘要:"
            echo "- 服务名称: aishoucang.service"
            echo "- 监听端口: 8080"
            echo "- 服务状态: 运行中"
          ENDSSH

          echo "✅ 部署完成"
