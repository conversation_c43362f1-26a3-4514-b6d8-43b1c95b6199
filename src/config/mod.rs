use serde::Deserialize;
use std::env;
use config::{Config, ConfigError, Environment};
use log::info;

/// 应用配置
#[derive(Debug, Deserialize, Clone)]
pub struct AppConfig {
    /// 服务器配置
    pub server: ServerConfig,

    /// MNS配置
    pub mns: MnsConfig,

    /// MongoDB配置
    pub mongodb: MongoDbConfig,

    /// MySQL配置
    pub mysql: MySqlConfig,

    /// OSS配置
    pub oss: OssConfig,
}

/// 服务器配置
#[derive(Debug, Deserialize, Clone)]
pub struct ServerConfig {
    /// 主机地址
    pub host: String,

    /// 端口
    pub port: u16,
}

/// MNS配置
#[derive(Debug, Deserialize, Clone)]
pub struct MnsConfig {
    /// 阿里云AccessKey ID
    pub access_key_id: String,

    /// 阿里云AccessKey Secret
    pub access_key_secret: String,

    /// MNS端点
    pub endpoint: String,

    /// 主题名称
    pub topic_name: String,
}

/// MongoDB配置
#[derive(Debug, Deserialize, Clone)]
pub struct MongoDbConfig {
    /// 连接URI
    pub uri: String,

    /// 数据库名称
    pub database: String,

    /// 最大连接池大小
    pub max_pool_size: Option<u32>,

    /// 最小连接池大小
    pub min_pool_size: Option<u32>,
}

/// OSS配置
#[derive(Debug, Deserialize, Clone)]
pub struct OssConfig {
    /// 阿里云AccessKey ID
    pub access_key_id: String,

    /// 阿里云AccessKey Secret
    pub access_key_secret: String,

    /// OSS端点
    pub endpoint: String,

    /// OSS Bucket名称
    pub bucket: String,

    /// OSS区域
    pub region: String,

    /// OSS角色ARN
    pub role_arn: String,
}

/// MySQL配置
#[derive(Debug, Deserialize, Clone)]
pub struct MySqlConfig {
    /// 连接URI
    pub uri: String,

    /// 数据库名称
    pub database: String,

    /// 最大连接池大小
    pub max_pool_size: Option<u32>,

    /// 最小连接池大小
    pub min_pool_size: Option<u32>,
}

impl AppConfig {
    /// 加载配置
    pub fn load() -> Result<Self, ConfigError> {
        // 加载.env文件
        dotenv::dotenv().ok();

        // 设置默认值
        let mut s = Config::builder()
            .set_default("server.host", "0.0.0.0")?
            .set_default("server.port", 8080)?
            .set_default("mongodb.max_pool_size", 10)?
            .set_default("mongodb.min_pool_size", 1)?
            .set_default("mysql.max_pool_size", 5)?
            .set_default("mysql.min_pool_size", 1)?;

        // 从环境变量加载配置
        s = s.add_source(Environment::with_prefix("APP").separator("_"));

        // 从环境变量加载服务器配置
        let server_host = env::var("SERVER_HOST")
            .unwrap_or_else(|_| "0.0.0.0".to_string());
        let server_port = env::var("SERVER_PORT")
            .ok()
            .and_then(|port| port.parse::<u16>().ok())
            .unwrap_or(8080);

        s = s.set_override("server.host", server_host)?
            .set_override("server.port", server_port)?;

        // 从环境变量加载MNS配置
        let access_key_id = env::var("ALIYUN_ACCESS_KEY_ID")
            .unwrap_or_else(|_| "".to_string());
        let access_key_secret = env::var("ALIYUN_ACCESS_KEY_SECRET")
            .unwrap_or_else(|_| "".to_string());
        let mns_endpoint = env::var("MNS_ENDPOINT")
            .unwrap_or_else(|_| "".to_string());
        let mns_topic_name = env::var("MNS_TOPIC_NAME")
            .unwrap_or_else(|_| "".to_string());

        s = s.set_override("mns.access_key_id", access_key_id.clone())?
            .set_override("mns.access_key_secret", access_key_secret.clone())?
            .set_override("mns.endpoint", mns_endpoint)?
            .set_override("mns.topic_name", mns_topic_name)?;

        // 从环境变量加载MongoDB配置
        let mongodb_connect_string = env::var("MONGODB_CONNECT_STRING")
            .unwrap_or_else(|_| "mongodb://root:<EMAIL>:3717/admin?replicaSet=mgset-74217650".to_string());
        let mongodb_test_db_name = env::var("MONGODB_TEST_DB_NAME")
            .unwrap_or_else(|_| "develop-aishoucang".to_string());
        let mongodb_prod_db_name = env::var("MONGODB_PROD_DB_NAME")
            .unwrap_or_else(|_| "product-aishoucang".to_string());

        // 根据环境选择数据库名称
        let mongodb_database = if env::var("APP_ENV").unwrap_or_else(|_| "development".to_string()) == "production" {
            mongodb_prod_db_name
        } else {
            mongodb_test_db_name
        };

        s = s.set_override("mongodb.uri", mongodb_connect_string)?
            .set_override("mongodb.database", mongodb_database)?;

        // 从环境变量加载MySQL配置
        let mysql_connect_string = env::var("MYSQL_CONNECT_STRING")
            .unwrap_or_else(|_| "mysql://aishoucang_mysql:Qtt$<EMAIL>:3306/aishoucang".to_string());
        let mysql_test_db_name = env::var("MYSQL_TEST_DB_NAME")
            .unwrap_or_else(|_| "aishoucang_test".to_string());
        let mysql_prod_db_name = env::var("MYSQL_PROD_DB_NAME")
            .unwrap_or_else(|_| "aishoucang".to_string());

        // 根据环境选择数据库名称
        let mysql_database = if env::var("APP_ENV").unwrap_or_else(|_| "development".to_string()) == "production" {
            mysql_prod_db_name
        } else {
            mysql_test_db_name
        };

        s = s.set_override("mysql.uri", mysql_connect_string)?
            .set_override("mysql.database", mysql_database)?;

        // 从环境变量加载OSS配置
        let oss_region = env::var("OSS_REGION")
            .unwrap_or_else(|_| "oss-cn-qingdao".to_string());
        let oss_access_key_id = env::var("OSS_ACCESS_KEY_ID")
            .unwrap_or_else(|_| "LTAIbUFLYWBXy4S8".to_string());
        let oss_access_key_secret = env::var("OSS_ACCESS_KEY_SECRET")
            .unwrap_or_else(|_| "Qub550SkQ2odHCtWoxbMpfa9zatr2m".to_string());
        let oss_bucket = env::var("OSS_BUCKET")
            .unwrap_or_else(|_| "assets-xunhe".to_string());
        let oss_role_arn = env::var("OSS_ROLE_ARN")
            .unwrap_or_else(|_| "acs:ram::1047429328316409:role/rustoss".to_string());
        let oss_endpoint = env::var("OSS_ENDPOINT")
            .unwrap_or_else(|_| format!("https://{}.aliyuncs.com", oss_region).to_string());

        s = s.set_override("oss.access_key_id", oss_access_key_id)?
            .set_override("oss.access_key_secret", oss_access_key_secret)?
            .set_override("oss.endpoint", oss_endpoint)?
            .set_override("oss.bucket", oss_bucket)?
            .set_override("oss.region", oss_region)?
            .set_override("oss.role_arn", oss_role_arn)?;

        // 构建配置
        let config = s.build()?.try_deserialize()?;

        info!("配置加载完成");
        Ok(config)
    }
}
