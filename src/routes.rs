use actix_web::{web, HttpRequest};
use crate::handlers::mns::webhook::handle_mns_message;
use crate::handlers::test::ping::handle_ping;
use crate::config::{MnsConfig, MySqlConfig, OssConfig};

/// 配置应用路由
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    // 添加测试路由
    cfg.service(
        web::scope("/test")
            .route("/ping", web::get().to(handle_ping))
    );

    // 添加API路由
    cfg.service(
        web::scope("/api")
            .service(
                web::resource("/mns/webhook")
                    .route(web::post().to(|
                        req: HttpRequest,
                        body: String,
                        mns_config: web::Data<MnsConfig>,
                        mysql_config: web::Data<MySqlConfig>,
                        oss_config: web::Data<OssConfig>,
                    | handle_mns_message(req, body, mns_config, mysql_config, oss_config)))
            )
    );
}
