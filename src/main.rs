mod config;
mod db;
mod handlers;
mod repositories;
mod routes;
mod utils;

use actix_web::{App, HttpServer, web, middleware::Logger};
use log::{info, error};
use std::io;

#[actix_web::main]
async fn main() -> io::Result<()> {
    // 初始化日志
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

    // 加载配置
    let config = match config::AppConfig::load() {
        Ok(cfg) => cfg,
        Err(e) => {
            error!("加载配置失败: {}", e);
            return Err(io::Error::new(io::ErrorKind::Other, format!("配置加载失败: {}", e)));
        }
    };

    let server_config = config.server.clone();
    let mns_config = config.mns.clone();
    let mongodb_config = config.mongodb.clone();
    let mysql_config = config.mysql.clone();
    let oss_config = config.oss.clone();

    // 初始化MongoDB连接
    info!("初始化MongoDB连接...");
    if let Err(e) = db::mongodb::MongoDbClient::init(&mongodb_config).await {
        error!("初始化MongoDB连接失败: {}", e);
        return Err(io::Error::new(io::ErrorKind::Other, format!("初始化MongoDB连接失败: {}", e)));
    }
    info!("MongoDB连接初始化成功");

    // 初始化MySQL连接
    info!("初始化MySQL连接...");
    if let Err(e) = db::mysql::MySqlClient::init(&mysql_config).await {
        error!("初始化MySQL连接失败: {}", e);
        return Err(io::Error::new(io::ErrorKind::Other, format!("初始化MySQL连接失败: {}", e)));
    }
    info!("MySQL连接初始化成功");

    // 初始化OSS客户端
    info!("初始化OSS客户端...");
    if let Err(e) = utils::oss_api::OssClient::init(oss_config.clone()) {
        error!("初始化OSS客户端失败: {}", e);
        return Err(io::Error::new(io::ErrorKind::Other, format!("初始化OSS客户端失败: {}", e)));
    }
    info!("OSS客户端初始化成功");

    info!("启动服务器 {}:{}", server_config.host, server_config.port);

    // 启动HTTP服务器
    HttpServer::new(move || {
        App::new()
            .wrap(Logger::default())
            .app_data(web::Data::new(mns_config.clone()))
            .app_data(web::Data::new(mongodb_config.clone()))
            .app_data(web::Data::new(mysql_config.clone()))
            .app_data(web::Data::new(oss_config.clone()))
            .configure(routes::configure_routes)
    })
    .bind((server_config.host, server_config.port))?
    .run()
    .await
}
