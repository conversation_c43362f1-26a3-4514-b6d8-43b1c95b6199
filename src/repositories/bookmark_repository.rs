use crate::db::mysql::{MySqlClient, MySqlError};
use sqlx::{FromRow, MySql, Pool};
use log::info;

/// 书签模型
#[derive(Debug, FromRow)]
pub struct Bookmark {
    /// 书签ID
    pub id: u64,

    /// 用户ID
    pub user_id: u64,

    /// 收藏夹ID
    pub favorite_id: u64,

    /// 博主名称
    pub influencer_name: String,

    /// 博主头像
    pub influencer_avatar: Option<String>,

    /// 封面
    pub cover: String,

    /// 标题
    pub title: String,

    /// 简介
    pub desc: String,

    /// 原生跳转链接
    pub scheme_url: String,

    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,

    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,

    /// MongoDB的ObjectId
    pub mongo_id: Option<String>,
}

/// 书签存储库
pub struct BookmarkRepository {
    pool: Pool<MySql>,
}

impl BookmarkRepository {
    /// 从MySQL客户端创建存储库
    pub fn from_client() -> Result<Self, MySqlError> {
        let client = MySqlClient::get_instance()?;
        Ok(Self {
            pool: client.get_pool().clone(),
        })
    }

    /// 从指定数据库创建存储库
    pub fn from_database(database: &str) -> Result<Self, MySqlError> {
        let client = MySqlClient::get_instance()?;

        // 获取指定数据库的连接池
        let pool = client.get_pool_for_database(database)?.clone();

        Ok(Self {
            pool,
        })
    }

    /// 根据MongoDB ID查找书签
    pub async fn find_by_mongo_id(&self, mongo_id: &str) -> Result<Option<Bookmark>, MySqlError> {
        info!("根据MongoDB ID查找书签: {}", mongo_id);

        let bookmark = sqlx::query_as::<_, Bookmark>(
            "SELECT * FROM bookmarks WHERE mongo_id = ?"
        )
        .bind(mongo_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| MySqlError::QueryError(format!("查询书签失败: {}", e)))?;

        if let Some(bookmark) = &bookmark {
            info!("找到书签: id={}", bookmark.id);
        } else {
            info!("未找到书签: mongo_id={}", mongo_id);
        }

        Ok(bookmark)
    }

    /// 根据ID查找书签
    pub async fn find_by_id(&self, id: &str) -> Result<Option<Bookmark>, MySqlError> {
        info!("根据ID查找书签: {}", id);

        // 将字符串ID转换为u64
        let id_u64 = id.parse::<u64>()
            .map_err(|e| MySqlError::QueryError(format!("ID不是有效的数字: {}", e)))?;

        let bookmark = sqlx::query_as::<_, Bookmark>(
            "SELECT * FROM bookmarks WHERE id = ?"
        )
        .bind(id_u64)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| MySqlError::QueryError(format!("查询书签失败: {}", e)))?;

        if let Some(bookmark) = &bookmark {
            info!("找到书签: id={}", bookmark.id);
        } else {
            info!("未找到书签: id={}", id);
        }

        Ok(bookmark)
    }

    /// 更新书签封面
    pub async fn update_cover(&self, id: u64, cover: &str) -> Result<bool, MySqlError> {
        info!("更新书签封面: id={}, cover={}", id, cover);

        let result = sqlx::query(
            "UPDATE bookmarks SET cover = ? WHERE id = ?"
        )
        .bind(cover)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| MySqlError::UpdateError(format!("更新书签封面失败: {}", e)))?;

        let rows_affected = result.rows_affected();
        info!("更新书签封面影响行数: {}", rows_affected);

        Ok(rows_affected > 0)
    }

    /// 更新书签博主头像
    pub async fn update_influencer_avatar(&self, id: u64, avatar: &str) -> Result<bool, MySqlError> {
        info!("更新书签博主头像: id={}, avatar={}", id, avatar);

        let result = sqlx::query(
            "UPDATE bookmarks SET influencer_avatar = ? WHERE id = ?"
        )
        .bind(avatar)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| MySqlError::UpdateError(format!("更新书签博主头像失败: {}", e)))?;

        let rows_affected = result.rows_affected();
        info!("更新书签博主头像影响行数: {}", rows_affected);

        Ok(rows_affected > 0)
    }
}
