use crate::db::mysql::{MySqlClient, MySqlError};
use sqlx::{FromRow, MySql, Pool};
use log::info;

/// 收藏夹模型
#[derive(Debug, FromRow)]
pub struct Favorite {
    /// 收藏夹ID
    pub id: u64,

    /// 用户ID
    pub user_id: u64,

    /// 收藏夹名称
    pub name: String,

    /// 封面图片URL
    pub cover: String,

    /// 排序值
    pub order: i32,

    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,

    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,

    /// MongoDB的ObjectId
    pub mongo_id: Option<String>,
}

/// 收藏夹存储库
pub struct FavoriteRepository {
    pool: Pool<MySql>,
}

impl FavoriteRepository {
    /// 从MySQL客户端创建存储库
    pub fn from_client() -> Result<Self, MySqlError> {
        let client = MySqlClient::get_instance()?;
        Ok(Self {
            pool: client.get_pool().clone(),
        })
    }

    /// 从指定数据库创建存储库
    pub fn from_database(database: &str) -> Result<Self, MySqlError> {
        let client = MySqlClient::get_instance()?;

        // 获取指定数据库的连接池
        let pool = client.get_pool_for_database(database)?.clone();

        Ok(Self {
            pool,
        })
    }

    /// 根据MongoDB ID查找收藏夹
    pub async fn find_by_mongo_id(&self, mongo_id: &str) -> Result<Option<Favorite>, MySqlError> {
        info!("根据MongoDB ID查找收藏夹: {}", mongo_id);

        let favorite = sqlx::query_as::<_, Favorite>(
            "SELECT * FROM favorites WHERE mongo_id = ?"
        )
        .bind(mongo_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| MySqlError::QueryError(format!("查询收藏夹失败: {}", e)))?;

        if let Some(favorite) = &favorite {
            info!("找到收藏夹: id={}", favorite.id);
        } else {
            info!("未找到收藏夹: mongo_id={}", mongo_id);
        }

        Ok(favorite)
    }

    /// 根据ID查找收藏夹
    pub async fn find_by_id(&self, id: &str) -> Result<Option<Favorite>, MySqlError> {
        info!("根据ID查找收藏夹: {}", id);

        // 将字符串ID转换为u64
        let id_u64 = id.parse::<u64>()
            .map_err(|e| MySqlError::QueryError(format!("ID不是有效的数字: {}", e)))?;

        let favorite = sqlx::query_as::<_, Favorite>(
            "SELECT * FROM favorites WHERE id = ?"
        )
        .bind(id_u64)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| MySqlError::QueryError(format!("查询收藏夹失败: {}", e)))?;

        if let Some(favorite) = &favorite {
            info!("找到收藏夹: id={}", favorite.id);
        } else {
            info!("未找到收藏夹: id={}", id);
        }

        Ok(favorite)
    }

    /// 更新收藏夹封面
    pub async fn update_cover(&self, id: u64, cover: &str) -> Result<bool, MySqlError> {
        info!("更新收藏夹封面: id={}, cover={}", id, cover);

        let result = sqlx::query(
            "UPDATE favorites SET cover = ? WHERE id = ?"
        )
        .bind(cover)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| MySqlError::UpdateError(format!("更新收藏夹封面失败: {}", e)))?;

        let rows_affected = result.rows_affected();
        info!("更新收藏夹封面影响行数: {}", rows_affected);

        Ok(rows_affected > 0)
    }
}
