use crate::db::mysql::{MySqlClient, MySqlError};
use sqlx::{FromRow, MySql, Pool};
use log::info;

/// 用户模型
#[derive(Debug, FromRow)]
pub struct User {
    /// 用户ID
    pub id: u64,

    /// 手机号
    pub phone: String,

    /// 密码（哈希值）
    pub password: String,

    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,

    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,

    /// MongoDB的ObjectId
    pub mongo_id: Option<String>,
}

/// 用户存储库
pub struct UserRepository {
    pool: Pool<MySql>,
}

impl UserRepository {
    /// 从MySQL客户端创建存储库
    pub fn from_client() -> Result<Self, MySqlError> {
        let client = MySqlClient::get_instance()?;
        Ok(Self {
            pool: client.get_pool().clone(),
        })
    }

    /// 根据MongoDB ID查找用户
    pub async fn find_by_mongo_id(&self, mongo_id: &str) -> Result<Option<User>, MySqlError> {
        info!("根据MongoDB ID查找用户: {}", mongo_id);

        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE mongo_id = ?"
        )
        .bind(mongo_id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| MySqlError::QueryError(format!("查询用户失败: {}", e)))?;

        if let Some(user) = &user {
            info!("找到用户: id={}", user.id);
        } else {
            info!("未找到用户: mongo_id={}", mongo_id);
        }

        Ok(user)
    }
}
