use actix_web::{web, HttpRequest, HttpResponse, Error};
use log::{info, error, warn};
use std::collections::HashMap;
use base64::{Engine as _, engine::general_purpose};
use crate::config::{MnsConfig, MySqlConfig, OssConfig};
use crate::utils::mns::{MnsUtils, MnsMessage};
use crate::utils::oss_api;
use crate::repositories::bookmark_repository::BookmarkRepository;
use serde_json::Value;


/// MNS消息处理函数
pub async fn handle_mns_message(
    req: HttpRequest,
    body: String,
    mns_config: web::Data<MnsConfig>,
    _mysql_config: web::Data<MySqlConfig>,
    _oss_config: web::Data<OssConfig>,
) -> Result<HttpResponse, Error> {
    info!("收到MNS消息推送请求");

    // 获取请求头
    let mut headers = HashMap::new();
    for (key, value) in req.headers() {
        if let Ok(value_str) = value.to_str() {
            headers.insert(key.to_string().to_lowercase(), value_str.to_string());
        }
    }

    // 记录请求头
    info!("请求头: {:?}", headers);
    info!("请求体: {}", body);

    // 创建MNS工具
    let mns_utils = MnsUtils::new(mns_config.get_ref().clone());

    // 验证签名
    match mns_utils.verify_signature("POST", &headers, &body) {
        Ok(true) => {
            info!("MNS消息签名验证通过");
        },
        Ok(false) => {
            warn!("MNS消息签名验证失败");
            return Ok(HttpResponse::Unauthorized().body("签名验证失败"));
        },
        Err(e) => {
            error!("MNS消息签名验证错误: {}", e);
            return Ok(HttpResponse::InternalServerError().body(format!("签名验证错误: {}", e)));
        }
    }

    // MySQL和OSS客户端已在应用启动时初始化

    // 解析消息
    match mns_utils.parse_message(&body) {
        Ok(message) => {
            // 处理消息
            match process_message(message).await {
                Ok(_) => {
                    // 返回成功响应
                    Ok(HttpResponse::Ok().body("消息处理成功"))
                },
                Err(e) => {
                    error!("处理消息失败: {}", e);
                    Ok(HttpResponse::InternalServerError().body(format!("处理消息失败: {}", e)))
                }
            }
        },
        Err(e) => {
            error!("解析MNS消息失败: {}", e);
            Ok(HttpResponse::BadRequest().body(format!("消息解析失败: {}", e)))
        }
    }
}

/// 处理MNS消息
async fn process_message(message: MnsMessage) -> Result<(), String> {
    info!("处理MNS消息: ID={}, 主题={}, 订阅={}",
        message.message_id, message.topic_name, message.subscription_name);

    // 记录完整的消息信息
    info!("消息详情:");
    info!("  - 消息ID: {}", message.message_id);
    info!("  - 主题: {}", message.topic_name);
    info!("  - 主题所有者: {}", message.topic_owner);
    info!("  - 订阅名称: {}", message.subscription_name);
    info!("  - 订阅者: {}", message.subscriber);

    // 尝试对消息内容进行base64解码
    let decoded_message = match decode_base64_message(&message.message_body) {
        Ok(decoded) => {
            info!("  - 消息内容(原始): {}", message.message_body);
            info!("  - 消息内容(解码): {}", decoded);
            decoded
        },
        Err(e) => {
            info!("  - 消息内容: {} (不是base64格式: {})", message.message_body, e);
            message.message_body.clone()
        }
    };

    if let Some(md5) = &message.message_body_md5 {
        info!("  - 消息MD5: {}", md5);
    }

    // 获取发布时间
    match message.get_publish_time_ms() {
        Ok(time_ms) => {
            let time_sec = time_ms / 1000;
            info!("  - 发布时间(毫秒): {}", time_ms);
            info!("  - 发布时间(秒): {}", time_sec);

            // 转换为可读时间格式
            let datetime = chrono::DateTime::from_timestamp(time_sec, 0)
                .map(|dt| dt.to_rfc3339())
                .unwrap_or_else(|| "无法转换时间".to_string());
            info!("  - 发布时间(格式化): {}", datetime);
        },
        Err(e) => {
            warn!("解析发布时间失败: {}, 原始值: {}", e, message.publish_time);
        }
    }

    // 根据消息内容执行业务逻辑
    info!("开始处理消息...");

    // 这里是一个简单的示例，根据主题名称执行不同的操作
    match message.topic_name.as_str() {
        "changeToOss" => {
            info!("处理OSS变更消息");
            // TODO: 实现OSS相关的业务逻辑
        },
        _ => {
            info!("未知主题类型: {}", message.topic_name);
        }
    }

    // 如果消息体是JSON格式，尝试解析
    if decoded_message.trim().starts_with('{') {
        match serde_json::from_str::<serde_json::Value>(&decoded_message) {
            Ok(json) => {
                info!("消息体解析为JSON: {:?}", json);

                // 处理JSON消息
                process_json_message(json).await?;
            },
            Err(e) => {
                warn!("消息体不是有效的JSON格式: {}", e);
                return Err(format!("消息体不是有效的JSON格式: {}", e));
            }
        }
    } else {
        warn!("消息体不是JSON格式");
        return Err("消息体不是JSON格式".to_string());
    }

    info!("消息处理完成");
    Ok(())
}
/// 处理JSON消息
async fn process_json_message(json: Value) -> Result<(), String> {
    info!("开始处理JSON消息");

    // 检查是否包含必要的字段
    // 从MongoDB迁移到MySQL，使用table字段代替collection字段
    let collection = json["table"].as_str().ok_or("缺少table字段")?;

    // 获取数据库名称（如果存在）
    let database = json["database"].as_str();
    if let Some(db) = database {
        info!("消息指定的数据库: {}", db);
    }

    // 获取bookmark对象
    let bookmark = json["bookmark"].as_object().ok_or("缺少bookmark对象或格式不正确")?;

    // 获取ID
    let id = bookmark.get("id")
        .ok_or("缺少id字段")?
        .as_str()
        .ok_or("id字段不是字符串类型")?;

    if id.is_empty() {
        return Err("id字段为空".to_string());
    }

    info!("使用记录ID: {}", id);

    // 获取封面图片URL
    let cover_url = bookmark["cover"].as_str().ok_or("缺少cover字段")?;
    if cover_url.is_empty() {
        return Err("cover字段为空".to_string());
    }

    info!("处理数据: collection={}, id={}", collection, id);
    info!("封面图片URL: {}", cover_url);

    // 并行处理封面图片和影响者头像
    use tokio::join;

    // 准备影响者头像URL（如果存在）
    let influencer_avatar = bookmark.get("influencer_avatar")
        .and_then(|v| v.as_str())
        .filter(|url| !url.is_empty());

    if let Some(avatar_url) = influencer_avatar {
        info!("影响者头像URL: {}", avatar_url);

        // 并行下载和上传两个图片
        let (cover_result, avatar_result) = join!(
            oss_api::download_and_upload_to_oss(cover_url),
            oss_api::download_and_upload_to_oss(avatar_url)
        );

        // 处理封面图片结果
        let cover_oss_url = match cover_result {
            Ok(url) => {
                info!("封面图片上传到OSS成功: {}", url);
                url
            },
            Err(e) => {
                error!("封面图片上传到OSS失败: {}", e);
                return Err(format!("封面图片上传到OSS失败: {}", e));
            }
        };

        // 处理影响者头像结果
        let influencer_avatar_oss_url = match avatar_result {
            Ok(url) => {
                info!("影响者头像上传到OSS成功: {}", url);
                Some(url)
            },
            Err(e) => {
                warn!("影响者头像上传到OSS失败: {}, 继续处理封面图片", e);
                None
            }
        };

        // 更新MySQL记录
        update_mysql_record(collection, id, &cover_oss_url, influencer_avatar_oss_url.as_deref(), database).await?;
    } else {
        // 只处理封面图片
        let cover_oss_url = match oss_api::download_and_upload_to_oss(cover_url).await {
            Ok(url) => {
                info!("封面图片上传到OSS成功: {}", url);
                url
            },
            Err(e) => {
                error!("封面图片上传到OSS失败: {}", e);
                return Err(format!("封面图片上传到OSS失败: {}", e));
            }
        };

        // 更新MySQL记录
        update_mysql_record(collection, id, &cover_oss_url, None, database).await?;
    }

    info!("JSON消息处理完成");
    Ok(())
}

/// 更新MySQL记录
async fn update_mysql_record(
    collection_name: &str,
    id: &str,
    cover_oss_url: &str,
    influencer_avatar_oss_url: Option<&str>,
    database: Option<&str>
) -> Result<(), String> {
    info!("更新MySQL记录: collection={}, id={}", collection_name, id);

    if let Some(db) = database {
        info!("使用指定数据库: {}", db);
    } else {
        info!("使用默认数据库配置");
    }

    if let Some(avatar_url) = influencer_avatar_oss_url {
        info!("将更新封面和影响者头像: cover={}, influencer_avatar={}", cover_oss_url, avatar_url);
    } else {
        info!("仅更新封面: cover={}", cover_oss_url);
    }

    // 根据集合名称选择不同的处理逻辑
    match collection_name {
        "bookmarks" => {
            // 获取书签仓库 - 根据数据库名称选择连接
            let bookmark_repo = if let Some(db) = database {
                info!("使用指定数据库连接: {}", db);
                BookmarkRepository::from_database(db)
                    .map_err(|e| format!("获取书签仓库(数据库={})失败: {}", db, e))?
            } else {
                info!("使用默认数据库连接");
                BookmarkRepository::from_client()
                    .map_err(|e| format!("获取书签仓库失败: {}", e))?
            };

            // 根据ID查找书签
            let bookmark = bookmark_repo.find_by_id(id).await
                .map_err(|e| format!("查询书签失败: {}", e))?;

            if let Some(bookmark) = bookmark {
                info!("找到书签记录: id={}", bookmark.id);

                // 更新封面
                let cover_updated = bookmark_repo.update_cover(bookmark.id, cover_oss_url).await
                    .map_err(|e| format!("更新书签封面失败: {}", e))?;

                if cover_updated {
                    info!("书签封面更新成功");
                } else {
                    warn!("书签封面未更新");
                }

                // 如果有影响者头像，也更新
                if let Some(avatar_url) = influencer_avatar_oss_url {
                    let avatar_updated = bookmark_repo.update_influencer_avatar(bookmark.id, avatar_url).await
                        .map_err(|e| format!("更新书签影响者头像失败: {}", e))?;

                    if avatar_updated {
                        info!("书签影响者头像更新成功");
                    } else {
                        warn!("书签影响者头像未更新");
                    }
                }

                Ok(())
            } else {
                warn!("未找到匹配的书签记录: id={}", id);
                Err(format!("未找到匹配的书签记录: id={}", id))
            }
        },
        "favorites" => {
            // 获取收藏夹仓库
            use crate::repositories::favorite_repository::FavoriteRepository;

            // 根据数据库名称选择连接
            let favorite_repo = if let Some(db) = database {
                info!("使用指定数据库连接: {}", db);
                FavoriteRepository::from_database(db)
                    .map_err(|e| format!("获取收藏夹仓库(数据库={})失败: {}", db, e))?
            } else {
                info!("使用默认数据库连接");
                FavoriteRepository::from_client()
                    .map_err(|e| format!("获取收藏夹仓库失败: {}", e))?
            };

            // 根据ID查找收藏夹
            let favorite = favorite_repo.find_by_id(id).await
                .map_err(|e| format!("查询收藏夹失败: {}", e))?;

            if let Some(favorite) = favorite {
                info!("找到收藏夹记录: id={}", favorite.id);

                // 更新封面
                let cover_updated = favorite_repo.update_cover(favorite.id, cover_oss_url).await
                    .map_err(|e| format!("更新收藏夹封面失败: {}", e))?;

                if cover_updated {
                    info!("收藏夹封面更新成功");
                } else {
                    warn!("收藏夹封面未更新");
                }

                Ok(())
            } else {
                warn!("未找到匹配的收藏夹记录: id={}", id);
                Err(format!("未找到匹配的收藏夹记录: id={}", id))
            }
        },
        _ => {
            warn!("未知的集合类型: {}", collection_name);
            Err(format!("未知的集合类型: {}", collection_name))
        }
    }
}

/// 尝试对消息进行base64解码
fn decode_base64_message(message: &str) -> Result<String, String> {
    // 检查消息是否可能是base64编码
    let is_likely_base64 = message.chars().all(|c| {
        c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '='
    });

    if !is_likely_base64 {
        return Err("消息不符合base64编码格式".to_string());
    }

    // 尝试解码
    match general_purpose::STANDARD.decode(message) {
        Ok(decoded_bytes) => {
            match String::from_utf8(decoded_bytes) {
                Ok(decoded_str) => Ok(decoded_str),
                Err(e) => Err(format!("解码后的内容不是有效的UTF-8字符串: {}", e))
            }
        },
        Err(e) => Err(format!("base64解码失败: {}", e))
    }
}
