use mongodb::{Client, Database};
use mongodb::options::{ClientOptions, ResolverConfig};
use once_cell::sync::OnceCell;
use std::sync::Arc;
use log::{info, error};
use crate::config::MongoDbConfig;
use thiserror::Error;

/// MongoDB 错误
#[derive(Debug, Error)]
pub enum MongoDbError {
    /// 连接错误
    #[error("MongoDB连接错误: {0}")]
    ConnectionError(String),

    /// 查询错误
    #[error("MongoDB查询错误: {0}")]
    QueryError(String),

    /// 更新错误
    #[error("MongoDB更新错误: {0}")]
    UpdateError(String),

    /// 其他错误
    #[error("MongoDB错误: {0}")]
    OtherError(String),
}

/// MongoDB 客户端
#[derive(Clone)]
pub struct MongoDbClient {
    client: Client,
}

// 全局MongoDB客户端实例
static MONGODB_CLIENT: OnceCell<Arc<MongoDbClient>> = OnceCell::new();

impl MongoDbClient {
    /// 初始化MongoDB客户端
    pub async fn init(config: &MongoDbConfig) -> Result<(), MongoDbError> {
        if MONGODB_CLIENT.get().is_some() {
            info!("MongoDB客户端已初始化");
            return Ok(());
        }

        info!("初始化MongoDB客户端: {}", config.uri);

        // 解析连接字符串
        let options = ClientOptions::parse_with_resolver_config(
            &config.uri,
            ResolverConfig::cloudflare(),
        )
        .await
        .map_err(|e| MongoDbError::ConnectionError(format!("解析连接字符串失败: {}", e)))?;

        // 设置连接池大小
        let mut client_options = options.clone();
        if let Some(max_pool_size) = config.max_pool_size {
            client_options.max_pool_size = Some(max_pool_size);
            info!("设置MongoDB最大连接池大小: {}", max_pool_size);
        }

        if let Some(min_pool_size) = config.min_pool_size {
            client_options.min_pool_size = Some(min_pool_size);
            info!("设置MongoDB最小连接池大小: {}", min_pool_size);
        }

        // 创建客户端
        let client = Client::with_options(client_options)
            .map_err(|e| MongoDbError::ConnectionError(format!("创建客户端失败: {}", e)))?;

        // 测试连接
        client
            .list_database_names(None, None)
            .await
            .map_err(|e| MongoDbError::ConnectionError(format!("测试连接失败: {}", e)))?;

        info!("MongoDB连接成功");

        // 存储客户端实例
        let mongodb_client = MongoDbClient { client };
        match MONGODB_CLIENT.set(Arc::new(mongodb_client)) {
            Ok(_) => {
                info!("MongoDB客户端实例已存储");
                Ok(())
            }
            Err(_) => Err(MongoDbError::OtherError("存储MongoDB客户端实例失败".to_string())),
        }
    }

    /// 获取MongoDB客户端实例
    pub fn get_instance() -> Result<Arc<MongoDbClient>, MongoDbError> {
        MONGODB_CLIENT
            .get()
            .cloned()
            .ok_or_else(|| MongoDbError::OtherError("MongoDB客户端未初始化".to_string()))
    }

    /// 获取数据库
    pub fn get_database(&self, db_name: &str) -> Database {
        self.client.database(db_name)
    }
}
