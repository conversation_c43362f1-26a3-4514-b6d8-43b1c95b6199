use sqlx::{mysql::{MySqlPoolOptions}, MySql, Pool};
use once_cell::sync::OnceCell;
use std::sync::Arc;
use std::env;
use log::{info, error};
use crate::config::MySqlConfig;
use thiserror::Error;

/// MySQL 错误
#[derive(Debug, Error)]
pub enum MySqlError {
    /// 连接错误
    #[error("MySQL连接错误: {0}")]
    ConnectionError(String),

    /// 查询错误
    #[error("MySQL查询错误: {0}")]
    QueryError(String),

    /// 更新错误
    #[error("MySQL更新错误: {0}")]
    UpdateError(String),

    /// 其他错误
    #[error("MySQL错误: {0}")]
    OtherError(String),
}

/// 数据库环境
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum DbEnv {
    /// 开发环境
    Develop,
    /// 预发布环境
    Pre,
    /// 生产环境
    Prod,
}

impl DbEnv {
    /// 获取数据库名称
    pub fn db_name(&self) -> &'static str {
        match self {
            DbEnv::Develop => "aishoucang_develop",
            DbEnv::Pre => "aishoucang_pre",
            DbEnv::Prod => "aishoucang",
        }
    }

    /// 从字符串解析环境
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "aishoucang_develop" => Some(DbEnv::Develop),
            "aishoucang_pre" => Some(DbEnv::Pre),
            "aishoucang" => Some(DbEnv::Prod),
            _ => None,
        }
    }
}

/// MySQL 客户端
#[derive(Clone)]
pub struct MySqlClient {
    pools: std::collections::HashMap<DbEnv, Pool<MySql>>,
    default_env: DbEnv,
}

// 全局MySQL客户端实例
static MYSQL_CLIENT: OnceCell<Arc<MySqlClient>> = OnceCell::new();

impl MySqlClient {
    /// 初始化MySQL客户端
    pub async fn init(config: &MySqlConfig) -> Result<(), MySqlError> {
        if MYSQL_CLIENT.get().is_some() {
            info!("MySQL客户端已初始化");
            return Ok(());
        }

        info!("初始化MySQL客户端");

        // 创建连接池选项
        let pool_options = MySqlPoolOptions::new();

        // 设置连接池大小
        let pool_options = if let Some(max_pool_size) = config.max_pool_size {
            info!("设置MySQL最大连接池大小: {}", max_pool_size);
            pool_options.max_connections(max_pool_size)
        } else {
            pool_options
        };

        let pool_options = if let Some(min_pool_size) = config.min_pool_size {
            info!("设置MySQL最小连接池大小: {}", min_pool_size);
            pool_options.min_connections(min_pool_size)
        } else {
            pool_options
        };

        // 从环境变量获取基础连接字符串
        let base_uri = std::env::var("MYSQL_CONNECT_STRING")
            .unwrap_or_else(|_| "mysql://aishoucang_mysql:Qtt$<EMAIL>:3306".to_string());

        // 创建所有环境的连接池
        let mut pools = std::collections::HashMap::new();

        // 创建开发环境连接池
        info!("创建开发环境数据库连接池");
        let dev_uri = format!("{}/{}", base_uri, DbEnv::Develop.db_name());
        let dev_pool = pool_options
            .clone()
            .connect(&dev_uri)
            .await
            .map_err(|e| MySqlError::ConnectionError(format!("创建开发环境连接池失败: {}", e)))?;

        // 测试开发环境连接
        sqlx::query("SELECT 1")
            .execute(&dev_pool)
            .await
            .map_err(|e| MySqlError::ConnectionError(format!("测试开发环境连接失败: {}", e)))?;
        info!("开发环境数据库连接成功");
        pools.insert(DbEnv::Develop, dev_pool);

        // 创建预发布环境连接池
        info!("创建预发布环境数据库连接池");
        let pre_uri = format!("{}/{}", base_uri, DbEnv::Pre.db_name());
        let pre_pool = pool_options
            .clone()
            .connect(&pre_uri)
            .await
            .map_err(|e| MySqlError::ConnectionError(format!("创建预发布环境连接池失败: {}", e)))?;

        // 测试预发布环境连接
        sqlx::query("SELECT 1")
            .execute(&pre_pool)
            .await
            .map_err(|e| MySqlError::ConnectionError(format!("测试预发布环境连接失败: {}", e)))?;
        info!("预发布环境数据库连接成功");
        pools.insert(DbEnv::Pre, pre_pool);

        // 创建生产环境连接池
        info!("创建生产环境数据库连接池");
        let prod_uri = format!("{}/{}", base_uri, DbEnv::Prod.db_name());
        let prod_pool = pool_options
            .clone()
            .connect(&prod_uri)
            .await
            .map_err(|e| MySqlError::ConnectionError(format!("创建生产环境连接池失败: {}", e)))?;

        // 测试生产环境连接
        sqlx::query("SELECT 1")
            .execute(&prod_pool)
            .await
            .map_err(|e| MySqlError::ConnectionError(format!("测试生产环境连接失败: {}", e)))?;
        info!("生产环境数据库连接成功");
        pools.insert(DbEnv::Prod, prod_pool);

        // 确定默认环境
        let default_env = if env::var("APP_ENV").unwrap_or_else(|_| "development".to_string()) == "production" {
            DbEnv::Prod
        } else {
            DbEnv::Develop
        };
        info!("默认数据库环境: {:?}", default_env);

        // 存储客户端实例
        let mysql_client = MySqlClient {
            pools,
            default_env,
        };

        match MYSQL_CLIENT.set(Arc::new(mysql_client)) {
            Ok(_) => {
                info!("MySQL客户端实例已存储");
                Ok(())
            }
            Err(_) => Err(MySqlError::OtherError("存储MySQL客户端实例失败".to_string())),
        }
    }

    /// 获取MySQL客户端实例
    pub fn get_instance() -> Result<Arc<MySqlClient>, MySqlError> {
        MYSQL_CLIENT
            .get()
            .cloned()
            .ok_or_else(|| MySqlError::OtherError("MySQL客户端未初始化".to_string()))
    }

    /// 获取默认连接池
    pub fn get_pool(&self) -> &Pool<MySql> {
        self.get_pool_for_env(self.default_env)
    }

    /// 获取指定环境的连接池
    pub fn get_pool_for_env(&self, env: DbEnv) -> &Pool<MySql> {
        &self.pools[&env]
    }

    /// 根据数据库名称获取连接池
    pub fn get_pool_for_database(&self, database: &str) -> Result<&Pool<MySql>, MySqlError> {
        let env = DbEnv::from_str(database)
            .ok_or_else(|| MySqlError::OtherError(format!("未知的数据库名称: {}", database)))?;

        Ok(self.get_pool_for_env(env))
    }
}
