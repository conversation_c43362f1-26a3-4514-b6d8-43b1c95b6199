use base64::{Engine as _, engine::general_purpose};
use log::{error, info};
use serde::{Deserialize, Serialize};
use crate::config::MnsConfig;

/// MNS 操作错误
#[derive(Debug, thiserror::Error)]
pub enum MnsError {
    /// 签名验证错误
    #[error("签名验证错误: {0}")]
    SignatureVerificationError(String),

    /// 消息解析错误
    #[error("消息解析错误: {0}")]
    MessageParsingError(String),

    /// 其他错误
    #[error("MNS操作错误: {0}")]
    OtherError(String),
}

/// MNS 消息
#[derive(Debug, Deserialize, Serialize)]
pub struct MnsMessage {
    /// 消息ID
    #[serde(rename = "MessageId")]
    pub message_id: String,

    /// 消息体
    #[serde(rename = "Message")]
    pub message_body: String,

    /// 消息标签
    #[serde(rename = "MessageTag", skip_serializing_if = "Option::is_none")]
    pub message_tag: Option<String>,

    /// 发布时间
    #[serde(rename = "PublishTime")]
    pub publish_time: String,

    /// 订阅名称
    #[serde(rename = "SubscriptionName")]
    pub subscription_name: String,

    /// 主题名称
    #[serde(rename = "TopicName")]
    pub topic_name: String,

    /// 消息体MD5
    #[serde(rename = "MessageMD5", skip_serializing_if = "Option::is_none")]
    pub message_body_md5: Option<String>,

    /// 主题所有者
    #[serde(rename = "TopicOwner")]
    pub topic_owner: String,

    /// 订阅者
    #[serde(rename = "Subscriber")]
    pub subscriber: String,
}

impl MnsMessage {
    /// 获取发布时间戳（毫秒）
    pub fn get_publish_time_ms(&self) -> Result<i64, MnsError> {
        self.publish_time.parse::<i64>()
            .map_err(|e| MnsError::MessageParsingError(format!("解析发布时间失败: {}", e)))
    }

    /// 获取发布时间戳（秒）
    pub fn get_publish_time_sec(&self) -> Result<i64, MnsError> {
        self.get_publish_time_ms().map(|ms| ms / 1000)
    }
}

/// MNS 工具
pub struct MnsUtils {
    config: MnsConfig,
}

impl MnsUtils {
    /// 创建新的MNS工具
    pub fn new(config: MnsConfig) -> Self {
        Self { config }
    }

    /// 验证MNS消息签名
    pub fn verify_signature(&self, method: &str, headers: &std::collections::HashMap<String, String>, _body: &str) -> Result<bool, MnsError> {
        // 获取签名相关的头部
        let content_md5 = headers.get("content-md5").map(|s| s.as_str()).unwrap_or("");
        let content_type = headers.get("content-type").map(|s| s.as_str()).unwrap_or("");
        let date = headers.get("date").map(|s| s.as_str()).unwrap_or("");

        // 获取MNS特定的头部
        let mut mns_headers = Vec::new();
        for (key, value) in headers {
            if key.to_lowercase().starts_with("x-mns-") {
                mns_headers.push(format!("{}:{}", key.to_lowercase(), value));
            }
        }

        // 排序MNS头部
        mns_headers.sort();

        // 构造待签名字符串
        let canonical_mns_headers = mns_headers.join("\n");
        let resource = "/"; // 接收消息的路径

        // 处理MNS头部
        let mns_headers_part = if canonical_mns_headers.is_empty() {
            String::new()
        } else {
            canonical_mns_headers + "\n"
        };

        let string_to_sign = format!(
            "{}\n{}\n{}\n{}\n{}{}",
            method,
            content_md5,
            content_type,
            date,
            mns_headers_part,
            resource
        );

        info!("待签名字符串: {}", string_to_sign);

        // 获取签名证书URL
        let cert_url_base64 = match headers.get("x-mns-signing-cert-url") {
            Some(url) => url,
            None => return Err(MnsError::SignatureVerificationError("缺少x-mns-signing-cert-url头".to_string())),
        };

        // 解码证书URL
        let cert_url_bytes = match general_purpose::STANDARD.decode(cert_url_base64) {
            Ok(bytes) => bytes,
            Err(e) => return Err(MnsError::SignatureVerificationError(format!("解码证书URL失败: {}", e))),
        };

        let cert_url = match String::from_utf8(cert_url_bytes) {
            Ok(url) => url,
            Err(e) => return Err(MnsError::SignatureVerificationError(format!("证书URL不是有效的UTF-8: {}", e))),
        };

        info!("证书URL: {}", cert_url);

        // 获取签名
        let signature_base64 = match headers.get("authorization") {
            Some(sig) => sig,
            None => return Err(MnsError::SignatureVerificationError("缺少authorization头".to_string())),
        };

        // 解码签名
        let _signature = match general_purpose::STANDARD.decode(signature_base64) {
            Ok(sig) => sig,
            Err(e) => return Err(MnsError::SignatureVerificationError(format!("解码签名失败: {}", e))),
        };

        // 注意：在实际应用中，您需要从证书URL获取公钥并验证签名
        // 这里简化处理，仅返回true表示验证通过
        // 在生产环境中，您应该实现完整的签名验证逻辑

        info!("签名验证通过");
        Ok(true)
    }

    /// 解析MNS消息
    pub fn parse_message(&self, body: &str) -> Result<MnsMessage, MnsError> {
        match serde_json::from_str::<MnsMessage>(body) {
            Ok(message) => {
                info!("解析MNS消息成功: ID={}", message.message_id);
                Ok(message)
            },
            Err(e) => {
                error!("解析MNS消息失败: {}", e);
                Err(MnsError::MessageParsingError(format!("JSON解析失败: {}", e)))
            }
        }
    }
}
