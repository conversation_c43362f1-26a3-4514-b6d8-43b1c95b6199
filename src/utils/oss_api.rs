use crate::config::OssConfig;
use bytes::Bytes;
use chrono::{DateTime, Utc};
use hmac::{Hmac, Mac};
use log::{error, info};
use once_cell::sync::OnceCell;
use reqwest::{
    header::{HeaderMap, HeaderName, HeaderValue, CONTENT_LENGTH, CONTENT_TYPE, DATE, HOST},
    Client,
};
use sha1::Sha1;
use std::{
    sync::Arc,
    time::SystemTime,
};
use thiserror::Error;
use uuid::Uuid;
use base64::{Engine as _, engine::general_purpose};

/// OSS 错误
#[derive(Debug, Error)]
pub enum OssError {
    /// 客户端错误
    #[error("OSS客户端错误: {0}")]
    ClientError(String),

    /// 上传错误
    #[error("OSS上传错误: {0}")]
    UploadError(String),

    /// 下载错误
    #[error("OSS下载错误: {0}")]
    DownloadError(String),

    /// 签名错误
    #[error("OSS签名错误: {0}")]
    SignatureError(String),

    /// 其他错误
    #[error("OSS错误: {0}")]
    OtherError(String),
}

/// OSS 客户端
#[derive(Clone)]
pub struct OssClient {
    client: Client,
    access_key_id: String,
    access_key_secret: String,
    bucket: String,
    region: String,
    endpoint: String,
}

// 全局OSS客户端实例
static OSS_CLIENT: OnceCell<Arc<OssClient>> = OnceCell::new();

impl OssClient {
    /// 初始化OSS客户端
    pub fn init(config: OssConfig) -> Result<(), OssError> {
        // 如果已经初始化，则直接返回
        if OSS_CLIENT.get().is_some() {
            return Ok(());
        }

        // 创建HTTP客户端
        let client = Client::builder()
            .build()
            .map_err(|e| OssError::ClientError(format!("创建HTTP客户端失败: {}", e)))?;

        // 创建OSS客户端实例
        let oss_client = OssClient {
            client,
            access_key_id: config.access_key_id,
            access_key_secret: config.access_key_secret,
            bucket: config.bucket,
            region: config.region,
            endpoint: config.endpoint,
        };

        // 存储全局实例
        match OSS_CLIENT.set(Arc::new(oss_client)) {
            Ok(_) => {
                info!("OSS客户端初始化成功");
                Ok(())
            }
            Err(_) => Err(OssError::ClientError("OSS客户端已经初始化".to_string())),
        }
    }

    /// 获取OSS客户端实例
    pub fn get() -> Result<Arc<OssClient>, OssError> {
        OSS_CLIENT
            .get()
            .cloned()
            .ok_or_else(|| OssError::ClientError("OSS客户端未初始化".to_string()))
    }

    /// 生成OSS签名
    fn generate_signature(&self, method: &str, content_md5: &str, content_type: &str, date: &str, canonicalized_resource: &str) -> Result<String, OssError> {
        // 构建待签名字符串
        let string_to_sign = format!(
            "{}\n{}\n{}\n{}\n{}",
            method, content_md5, content_type, date, canonicalized_resource
        );

        // 使用HMAC-SHA1计算签名
        let mut mac = Hmac::<Sha1>::new_from_slice(self.access_key_secret.as_bytes())
            .map_err(|e| OssError::SignatureError(format!("创建HMAC失败: {}", e)))?;

        mac.update(string_to_sign.as_bytes());
        let result = mac.finalize();
        let signature = general_purpose::STANDARD.encode(result.into_bytes());

        Ok(signature)
    }

    /// 上传数据到OSS
    pub async fn upload_data(
        &self,
        data: Bytes,
        content_type: &str,
        extension: &str,
    ) -> Result<String, OssError> {
        // 生成唯一的对象名称
        let uuid = Uuid::new_v4().to_string();
        let object_name = format!("images/{}.{}", uuid, extension);

        // 构建请求URL
        let url = format!("https://{}.{}.aliyuncs.com/{}", self.bucket, self.region, object_name);

        // 获取当前时间
        let now: DateTime<Utc> = SystemTime::now().into();
        let date = now.format("%a, %d %b %Y %H:%M:%S GMT").to_string();

        // 构建规范化资源
        let canonicalized_resource = format!("/{}/{}", self.bucket, object_name);

        // 计算Content-MD5 (可选，这里为空)
        let content_md5 = "";

        // 生成签名
        let signature = self.generate_signature("PUT", content_md5, content_type, &date, &canonicalized_resource)?;

        // 构建授权头
        let authorization = format!("OSS {}:{}", self.access_key_id, signature);

        // 构建请求头
        let mut headers = HeaderMap::new();
        headers.insert(DATE, HeaderValue::from_str(&date).map_err(|e| OssError::UploadError(format!("无效的日期头: {}", e)))?);
        headers.insert(
            HeaderName::from_static("authorization"),
            HeaderValue::from_str(&authorization).map_err(|e| OssError::UploadError(format!("无效的授权头: {}", e)))?,
        );
        headers.insert(
            CONTENT_TYPE,
            HeaderValue::from_str(content_type).map_err(|e| OssError::UploadError(format!("无效的内容类型: {}", e)))?,
        );
        headers.insert(
            CONTENT_LENGTH,
            HeaderValue::from_str(&data.len().to_string()).map_err(|e| OssError::UploadError(format!("无效的内容长度: {}", e)))?,
        );
        headers.insert(
            HOST,
            HeaderValue::from_str(&format!("{}.{}.aliyuncs.com", self.bucket, self.region)).map_err(|e| OssError::UploadError(format!("无效的主机头: {}", e)))?,
        );

        // 发送请求
        let response = self
            .client
            .put(&url)
            .headers(headers)
            .body(data)
            .send()
            .await
            .map_err(|e| OssError::UploadError(format!("发送请求失败: {}", e)))?;

        // 检查响应状态
        if response.status().is_success() {
            let oss_url = format!("https://{}.{}.aliyuncs.com/{}", self.bucket, self.region, object_name);
            info!("上传成功: {}", oss_url);
            Ok(oss_url)
        } else {
            let status = response.status();
            let body = response
                .text()
                .await
                .unwrap_or_else(|_| "无法读取响应体".to_string());

            error!("上传失败，状态码: {}, 响应: {}", status, body);
            Err(OssError::UploadError(format!(
                "上传失败，状态码: {}, 响应: {}",
                status, body
            )))
        }
    }
}

/// 从URL下载图片并上传到OSS
pub async fn download_and_upload_to_oss(image_url: &str) -> Result<String, OssError> {
    info!("下载图片: {}", image_url);

    // 获取OSS客户端
    let oss_client = OssClient::get()
        .map_err(|e| OssError::DownloadError(format!("获取OSS客户端失败: {}", e)))?;

    // 下载图片
    let response = reqwest::get(image_url)
        .await
        .map_err(|e| OssError::DownloadError(format!("下载图片失败: {}", e)))?;

    if !response.status().is_success() {
        return Err(OssError::DownloadError(format!(
            "下载图片失败，状态码: {}",
            response.status()
        )));
    }

    // 获取内容类型
    let content_type = response
        .headers()
        .get(reqwest::header::CONTENT_TYPE)
        .and_then(|v| v.to_str().ok())
        .unwrap_or("image/jpeg")
        .to_string();

    // 确定文件扩展名
    let extension = match content_type.as_str() {
        "image/jpeg" | "image/jpg" => "jpg",
        "image/png" => "png",
        "image/gif" => "gif",
        "image/webp" => "webp",
        _ => "jpg", // 默认为jpg
    };

    // 读取图片数据
    let bytes = response
        .bytes()
        .await
        .map_err(|e| OssError::DownloadError(format!("读取图片数据失败: {}", e)))?;

    // 上传到OSS
    oss_client.upload_data(bytes, &content_type, extension).await
}
