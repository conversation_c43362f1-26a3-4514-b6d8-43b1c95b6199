use crate::config::MongoDbConfig;
use log::{error, info};
use mongodb::{
    bson::{doc, Document},
    options::{ClientOptions, ResolverConfig},
    Client, Collection, Database,
};
use once_cell::sync::OnceCell;
use std::sync::Arc;
use thiserror::Error;

/// MongoDB 错误
#[derive(Debug, Error)]
pub enum MongoDbError {
    /// 连接错误
    #[error("MongoDB连接错误: {0}")]
    ConnectionError(String),

    /// 查询错误
    #[error("MongoDB查询错误: {0}")]
    QueryError(String),

    /// 更新错误
    #[error("MongoDB更新错误: {0}")]
    UpdateError(String),

    /// 插入错误
    #[error("MongoDB插入错误: {0}")]
    InsertError(String),

    /// 删除错误
    #[error("MongoDB删除错误: {0}")]
    DeleteError(String),

    /// 其他错误
    #[error("MongoDB错误: {0}")]
    OtherError(String),
}

/// MongoDB 客户端
#[derive(Clone)]
pub struct MongoDbClient {
    client: Client,
    database: String,
}

// 全局MongoDB客户端实例
static MONGODB_CLIENT: OnceCell<Arc<MongoDbClient>> = OnceCell::new();

impl MongoDbClient {
    /// 初始化MongoDB客户端
    pub async fn init(config: MongoDbConfig) -> Result<(), MongoDbError> {
        // 如果已经初始化，则直接返回
        if MONGODB_CLIENT.get().is_some() {
            return Ok(());
        }

        // 解析MongoDB连接选项
        let options = ClientOptions::parse_with_resolver_config(&config.uri, ResolverConfig::cloudflare())
            .await
            .map_err(|e| MongoDbError::ConnectionError(format!("解析MongoDB连接URI失败: {}", e)))?;

        // 创建MongoDB客户端
        let client = Client::with_options(options)
            .map_err(|e| MongoDbError::ConnectionError(format!("创建MongoDB客户端失败: {}", e)))?;

        // 测试连接
        client
            .database("admin")
            .run_command(doc! { "ping": 1 }, None)
            .await
            .map_err(|e| MongoDbError::ConnectionError(format!("MongoDB连接测试失败: {}", e)))?;

        // 创建MongoDB客户端实例
        let mongodb_client = MongoDbClient {
            client,
            database: config.database,
        };

        // 存储全局实例
        match MONGODB_CLIENT.set(Arc::new(mongodb_client)) {
            Ok(_) => {
                info!("MongoDB客户端初始化成功");
                Ok(())
            }
            Err(_) => Err(MongoDbError::ConnectionError(
                "MongoDB客户端已经初始化".to_string(),
            )),
        }
    }

    /// 获取MongoDB客户端实例
    pub fn get() -> Result<Arc<MongoDbClient>, MongoDbError> {
        MONGODB_CLIENT
            .get()
            .cloned()
            .ok_or_else(|| MongoDbError::ConnectionError("MongoDB客户端未初始化".to_string()))
    }

    /// 获取数据库
    pub fn database(&self) -> Database {
        self.client.database(&self.database)
    }

    /// 获取集合
    pub fn collection<T>(&self, name: &str) -> Collection<T> {
        self.database().collection(name)
    }

    /// 更新文档
    pub async fn update_document(
        &self,
        collection_name: &str,
        filter: Document,
        update: Document,
    ) -> Result<bool, MongoDbError> {
        let collection = self.database().collection::<Document>(collection_name);
        
        let result = collection
            .update_one(filter, update, None)
            .await
            .map_err(|e| MongoDbError::UpdateError(format!("更新文档失败: {}", e)))?;
            
        Ok(result.modified_count > 0)
    }
}
