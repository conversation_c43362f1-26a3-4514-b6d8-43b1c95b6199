[package]
name = "aishoucang-mns"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.4.1"
actix-rt = "2.9.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
env_logger = "0.10.1"
log = "0.4.20"
tokio = { version = "1.44.2", features = ["full"] }
thiserror = "2.0.12"
chrono = { version = "0.4.40", features = ["serde"] }
hmac = "0.12.1"
sha1 = "0.10.6"
base64 = "0.21.7"
quick-xml = { version = "0.37.5", features = ["serialize"] }
uuid = { version = "1.16.0", features = ["v4"] }
dotenv = "0.15.0"
config = "0.13.4"
mongodb = { version = "2.8.1", features = ["tokio-runtime"] }
futures = "0.3.30"
reqwest = { version = "0.12", default-features = false, features = ["rustls-tls"] }
# 已移除 aliyun-oss-client 依赖，改用直接 API 调用
once_cell = "1.19.0"
bytes = "1.5.0"
regex = "1.10.3"
sqlx = { version = "0.7.4", features = ["runtime-tokio", "tls-rustls", "mysql", "chrono", "uuid"] }
