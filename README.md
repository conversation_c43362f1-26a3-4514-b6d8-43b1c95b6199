# 阿里云MNS消息接收服务

这是一个用Rust实现的阿里云MNS消息接收服务，用于接收阿里云MNS推送的消息。

## 功能特点

- 提供HTTP接口接收阿里云MNS推送的消息
- 支持消息签名验证
- 灵活的消息处理逻辑
- 完善的日志记录

## 快速开始

### 环境要求

- Rust 1.70.0 或更高版本
- Cargo 1.70.0 或更高版本

### 安装依赖

```bash
cargo build
```

### 配置

在项目根目录创建`.env`文件，配置以下环境变量：

```
# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 日志级别
RUST_LOG=info

# 阿里云MNS配置
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
MNS_ENDPOINT=http://your-account-id.mns.cn-hangzhou.aliyuncs.com
MNS_TOPIC_NAME=your-topic-name
```

### 运行

```bash
cargo run
```

服务将在配置的端口上启动，默认为8080。

## 接口说明

### MNS消息接收接口

- URL: `/api/mns/webhook`
- 方法: POST
- 请求体: MNS推送的消息内容
- 响应: 200 OK 表示消息处理成功

## 消息处理

接收到MNS消息后，服务会：

1. 验证消息签名
2. 解析消息内容
3. 根据消息内容执行相应的业务逻辑
4. 返回处理结果

## 项目结构

```
src/
├── config/         # 配置管理
├── handlers/       # 请求处理器
│   └── mns/        # MNS消息处理
├── middleware/     # 中间件
├── models/         # 数据模型
├── utils/          # 工具函数
│   └── mns.rs      # MNS工具
├── main.rs         # 主程序
└── routes.rs       # 路由配置
```

## 阿里云MNS配置

在阿里云MNS控制台中，您需要：

1. 创建主题
2. 创建HTTP订阅，指向您的服务地址（例如：`http://your-server-ip:8080/api/mns/webhook`）
3. 确保您的服务器可以从公网访问（如果使用公网推送）

## 每次安装新的依赖前要看下是否可以不用openssl

## 许可证

MIT
